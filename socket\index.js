import WebSocketConfig from '../src/utils/websocket.js';

const socketService = {
    socket: null,
    reconnectAttempts: 0,
    maxReconnectAttempts: 5,
    reconnectInterval: 3000,

    init(username) {
        if (typeof WebSocket === "undefined") {
            alert("您的浏览器不支持WebSocket");
            return;
        }

        try {
            // 使用WebSocketConfig获取连接地址
            //const params = username ? { username } : {};
            const wsUrl = WebSocketConfig.getWebSocketUrlWithParams('/api/v1/ws');

            console.log("WebSocket连接地址:", wsUrl);

            this.socket = new WebSocket(wsUrl);
            this.socket.onopen = this.open.bind(this);
            this.socket.onerror = this.error.bind(this);
            this.socket.onmessage = null; // 将在getMessage中设置
            this.socket.onclose = this.onClose.bind(this);
        } catch (error) {
            console.error("WebSocket初始化失败:", error);
        }
    },

    open() {
        console.log("WebSocket连接成功");
        this.reconnectAttempts = 0; // 重置重连次数
    },

    error(error) {
        console.error("WebSocket连接错误:", error);
        this.reconnect();
    },

    onClose(event) {
        console.log("WebSocket连接关闭:", event.code, event.reason);
        if (event.code !== 1000) { // 非正常关闭
            this.reconnect();
        }
    },

    // 重连机制
    reconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`WebSocket重连中... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            setTimeout(() => {
                this.init("warning-all");
            }, this.reconnectInterval);
        } else {
            console.error("WebSocket重连失败，已达到最大重连次数");
        }
    },

    getMessage() {
        return new Promise((resolve) => {
            this.socket.onmessage = (msg) => {

                // 利用promise 返回出去结果
                if (msg.data != '连接成功' && JSON.parse(msg.data)) {
                    const data = JSON.parse(msg.data);
                    resolve(data); // 将数据传递给调用者
                }
                // this.scrollInstance.refresh(); // 手动刷新滚动效果
            };
        });
        // this.scrollInstance.refresh(); // 手动刷新滚动效果
    },

    send(params) {
        if (this.socket) {
            this.socket.send(params);
        }
    },
    close() {
        this.socket.close();
        console.log("socket已经关闭");
    }
};

// 导出
export default socketService;