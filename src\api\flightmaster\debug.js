import request from '@/utils/request'

// 获取调试信息
export function getDebugInfo(dockSn) {
  return request({
    url: '/debug/getDebugInfo',
    method: 'get',
    params: {
      dockSn: dockSn
    }
  })
}

// 发送调试控制指令
export function sendDebugCommand(dockSn,service_identifier,data) {
  return request({
     url: `/control/api/v1/devices/${dockSn}/jobs/${service_identifier}`,
    method: 'post',
    data: data
  })
}

// 开启调试模式
export function openDebugMode(dockSn) {
  return request({
    url: `/control/api/v1/devices/${dockSn}/jobs/debug_mode_open`,
    method: 'post',
  })
}

// 关闭调试模式
export function closeDebugMode(dockSn) {
  return request({
    url: `/control/api/v1/devices/${dockSn}/jobs/debug_mode_close`,
    method: 'post',
   
  })
}
