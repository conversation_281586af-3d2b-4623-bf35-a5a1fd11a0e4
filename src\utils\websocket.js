/**
 * WebSocket 配置工具
 * 根据环境自动配置WebSocket连接地址
 */

// WebSocket配置
const WebSocketConfig = {
  // 获取WebSocket基础URL
  getBaseUrl() {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    
   
      // 生产环境：使用当前域名
      return `${protocol}//${host}${process.env.VUE_APP_BASE_API}`;
    
  },
  
  // 获取完整的WebSocket URL
  getWebSocketUrl(path = '/api/v1/ws') {
    const baseUrl = this.getBaseUrl();
    return `${baseUrl}${path}`;
  },
  
  // 获取带参数的WebSocket URL
  getWebSocketUrlWithParams(path = '/api/v1/ws', params = {}) {
    const baseUrl = this.getWebSocketUrl(path);
    const queryString = Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&');

    return queryString ? `${baseUrl}?${queryString}` : baseUrl;
  },

  // 获取带认证的WebSocket URL
  getAuthenticatedWebSocketUrl(path = '/api/v1/ws', token = null, username = null) {
    const params = {};

    if (token) {
      params.token = token;
    }

    if (username) {
      params.username = username;
    }

    return this.getWebSocketUrlWithParams(path, params);
  }
};

export default WebSocketConfig;
